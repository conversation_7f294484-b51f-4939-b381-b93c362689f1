const Base = require('../../common/controller/base.js');
const moment = require('moment');

module.exports = class extends Base {
    async indexAction() {
        const goodsOnsale = await this.model('goods').where({is_on_sale: 1,is_delete:0}).count();
        const orderToDelivery = await this.model('order').where({order_status: ['IN', [201, 300]]}).count();
        const user = await this.model('user').count();
        let info = {
            user: user,
            goodsOnsale: goodsOnsale,
            orderToDelivery: orderToDelivery,
        }
        return this.success(info);
    }
    async mainAction() {
        const index = this.get('pindex');
        console.log('index:' + index);
        let todayTimeStamp = new Date(new Date().setHours(0, 0, 0, 0)) / 1000; //今天零点的时间戳
        let yesTimeStamp = todayTimeStamp - 86400; //昨天零点的时间戳
        let sevenTimeStamp = todayTimeStamp - 86400 * 7; //7天前零点的时间戳
        let thirtyTimeStamp = todayTimeStamp - 86400 * 30; //30天前零点的时间戳
        let newUser = 1;
        let oldUser = 0;
        let addCart = 0;
        let addOrderNum = 0;
        let addOrderSum = 0;
        let payOrderNum = 0;
        let payOrderSum = 0;
        let newData = [];
        let oldData = [];
        if (index == 0) {
            newData = await this.model('user').where({
                id: ['>', 0],
                register_time: ['>', todayTimeStamp]
            }).select();
            newUser = newData.length;
            
            oldData = await this.model('user').where({
                id: ['>', 0],
                register_time: ['<', todayTimeStamp],
                last_login_time: ['>', todayTimeStamp]
            }).select();
            
            oldUser = oldData.length;
            addCart = await this.model('cart').where({is_delete: 0, add_time: ['>', todayTimeStamp]}).count();
            addOrderNum = await this.model('order').where({
                is_delete: 0,
                create_time: ['>', todayTimeStamp]
            }).count();
            addOrderSum = await this.model('order').where({
                is_delete: 0,
                create_time: ['>', todayTimeStamp]
            }).sum('actual_price');
            payOrderNum = await this.model('order').where({
                is_delete: 0,
                create_time: ['>', todayTimeStamp],
                order_status: ['IN', [201, 802]]
            }).count();
            payOrderSum = await this.model('order').where({
                is_delete: 0,
                create_time: ['>', todayTimeStamp],
                order_status: ['IN', [201, 802]]
            }).sum('actual_price');
        }
        else if (index == 1) {
            newData = await this.model('user').where({
                id: ['>', 0],
                register_time: ['BETWEEN', yesTimeStamp, todayTimeStamp]
            }).select();
            
            newUser = newData.length;
            oldData = await this.model('user').where({
                id: ['>', 0],
                register_time: ['<', yesTimeStamp],
                last_login_time: ['BETWEEN', yesTimeStamp, todayTimeStamp]
            }).select();
            
            oldUser = oldData.length;
            addCart = await this.model('cart').where({
                is_delete: 0,
                add_time: ['BETWEEN', yesTimeStamp, todayTimeStamp]
            }).count();

            addOrderNum = await this.model('order').where({
                is_delete: 0,
                create_time: ['BETWEEN', yesTimeStamp, todayTimeStamp]
            }).count();

            addOrderSum = await this.model('order').where({
                is_delete: 0,
                create_time: ['BETWEEN', yesTimeStamp, todayTimeStamp]
            }).sum('actual_price');

            payOrderNum = await this.model('order').where({
                is_delete: 0,
                create_time: ['BETWEEN', yesTimeStamp, todayTimeStamp],
                order_status: ['IN', [201, 802]]
            }).count();

            payOrderSum = await this.model('order').where({
                is_delete: 0,
                create_time: ['BETWEEN', yesTimeStamp, todayTimeStamp],
                order_status: ['IN', [201, 802]]
            }).sum('actual_price');

        }
        else if (index == 2) {
            newData = await this.model('user').where({
                id: ['>', 0],
                register_time: ['>', sevenTimeStamp]
            }).select();
            
            newUser = newData.length;
            oldData = await this.model('user').where({
                id: ['>', 0],
                register_time: ['<', sevenTimeStamp],
                last_login_time: ['>', sevenTimeStamp]
            }).select();
            
            oldUser = oldData.length;
            addCart = await this.model('cart').where({
                is_delete: 0,
                add_time: ['>', sevenTimeStamp]
            }).count();
            addOrderNum = await this.model('order').where({
                is_delete: 0,
                create_time: ['>', sevenTimeStamp]
            }).count();
            addOrderSum = await this.model('order').where({
                is_delete: 0,
                create_time: ['>', sevenTimeStamp]
            }).sum('actual_price');
            payOrderNum = await this.model('order').where({
                is_delete: 0,
                create_time: ['>', sevenTimeStamp],
                order_status: ['IN', [201, 802]]
            }).count();
            payOrderSum = await this.model('order').where({
                is_delete: 0,
                create_time: ['>', sevenTimeStamp],
                order_status: ['IN', [201, 802]]
            }).sum('actual_price');
        }
        else if (index == 3) {
            newData = await this.model('user').where({
                id: ['>', 0],
                register_time: ['>', thirtyTimeStamp]
            }).select();
            
            newUser = newData.length;
            oldData = await this.model('user').where({
                id: ['>', 0],
                register_time: ['<', thirtyTimeStamp],
                last_login_time: ['>', thirtyTimeStamp]
            }).select();
            
            oldUser = oldData.length;
            addCart = await this.model('cart').where({
                is_delete: 0,
                add_time: ['>', thirtyTimeStamp]
            }).count();
            addOrderNum = await this.model('order').where({
                is_delete: 0,
                create_time: ['>', thirtyTimeStamp]
            }).count();
            addOrderSum = await this.model('order').where({
                is_delete: 0,
                create_time: ['>', thirtyTimeStamp]
            }).sum('actual_price');
            payOrderNum = await this.model('order').where({
                is_delete: 0,
                create_time: ['>', thirtyTimeStamp],
                order_status: ['IN', [201, 802]]
            }).count();
            payOrderSum = await this.model('order').where({
                is_delete: 0,
                create_time: ['>', thirtyTimeStamp],
                order_status: ['IN', [201, 802]]
            }).sum('actual_price');
        }
        if (addOrderSum == null) {
            addOrderSum = 0;
        }
        if (payOrderSum == null) {
            payOrderSum = 0;
        }
        if(newData.length > 0){
            for(const item of newData){
                item.register_time =  moment.unix(item.register_time).format('YYYY-MM-DD HH:mm:ss');
                item.last_login_time =  moment.unix(item.last_login_time).format('YYYY-MM-DD HH:mm:ss');
            }
        }

        if(oldData.length > 0){
            for(const item of oldData){
                item.register_time =  moment.unix(item.register_time).format('YYYY-MM-DD HH:mm:ss');
                item.last_login_time =  moment.unix(item.last_login_time).format('YYYY-MM-DD HH:mm:ss');
            }
        }

        let info = {
            newUser: newUser, // 新注册用户个数
            oldUser: oldUser, // 以前注册的用户个数
            addCart: addCart, // 新增到购物车的商品个数
            newData: newData, // 新注册用户数据
            oldData: oldData, // 以前注册的用户数据
            addOrderNum: addOrderNum, // 新增订单个数
            addOrderSum: addOrderSum, // 新增订单总金额
            payOrderNum: payOrderNum, // 已支付订单个数
            payOrderSum: payOrderSum  // 已支付订单总金额
        }
        return this.success(info);
    }

    async newMainAction() {
        // 1. 定义所需的时间戳和日期字符串
        const now = new Date();
        const todayTimeStamp = new Date(now.setHours(0, 0, 0, 0)) / 1000;
        const yesTimeStamp = todayTimeStamp - 86400; // 昨天零点
        const sevenTimeStamp = todayTimeStamp - 86400 * 7; // 7天前零点
        const thirtyTimeStamp = todayTimeStamp - 86400 * 30; // 30天前零点
        
        // 转换为 datetime 格式用于 order 表查询
        const todayDatetime = moment.unix(todayTimeStamp).format('YYYY-MM-DD HH:mm:ss');
        const yesDayDatetime = moment.unix(yesTimeStamp).format('YYYY-MM-DD HH:mm:ss');
        const sevenDayDatetime = moment.unix(sevenTimeStamp).format('YYYY-MM-DD HH:mm:ss');
        const thirtyDayDatetime = moment.unix(thirtyTimeStamp).format('YYYY-MM-DD HH:mm:ss');

        // 2. 封装核心数据查询函数
        const fetchDataByTimeRange = async (startTime, endTime = null, startDatetime, endDatetime = null) => {
            // 用户查询使用时间戳（int 类型字段）
            const userTimeCondition = endTime ? ['BETWEEN', startTime, endTime] : ['>', startTime];
            // 订单查询使用 datetime 格式
            const orderTimeCondition = endDatetime ? ['>=', startDatetime] : ['>=', startDatetime];
            const orderEndCondition = endDatetime ? ['<', endDatetime] : null;
            
            // 核心数据查询
            const newUserCount = await this.model('user').where({ register_time: userTimeCondition }).count();
            const oldUserCount = await this.model('user').where({ 
                register_time: ['<', startTime], 
                last_login_time: userTimeCondition 
            }).count();
            
            // 购物车查询（假设 add_time 是时间戳）
            const addCartCount = await this.model('cart').where({ 
                is_delete: 0, 
                add_time: userTimeCondition 
            }).count();
            
            // 订单查询使用 datetime 格式
            let orderWhere = { is_delete: 0, create_time: orderTimeCondition };
            if (orderEndCondition) {
                orderWhere.create_time = ['BETWEEN', startDatetime, endDatetime];
            }
            
            const addOrderNum = await this.model('order').where(orderWhere).count();
            const addOrderSum = await this.model('order').where(orderWhere).sum('actual_price') || 0;
            
            // 已支付订单查询 - 包含更完整的已支付状态
            const payOrderWhere = { 
                ...orderWhere, 
                order_status: ['IN', [201, 301, 302, 303, 401, 802]] // 已付款、已发货、已收货、已完成、拼团已付款
            };
            payOrderWhere.pay_time = payOrderWhere.create_time;
            delete payOrderWhere.create_time;
            const payOrderNum = await this.model('order').where(payOrderWhere).count();
            const payOrderSum = await this.model('order').where(payOrderWhere).sum('actual_price') || 0;
            
            // 计算转化率
            const checkoutConversionRate = (addOrderNum > 0) ? (payOrderNum / addOrderNum * 100).toFixed(2) : 0;
            
            return {
                newUser: newUserCount,
                oldUser: oldUserCount,
                addCart: addCartCount,
                addOrderNum: addOrderNum,
                addOrderSum: parseFloat(addOrderSum).toFixed(2),
                payOrderNum: payOrderNum,
                payOrderSum: parseFloat(payOrderSum).toFixed(2),
                checkoutConversionRate: checkoutConversionRate + '%'
            };
        };

        // 3. 一次性获取所有时间维度的数据
        const todayData = await fetchDataByTimeRange(todayTimeStamp, null, todayDatetime);
        const yesterdayData = await fetchDataByTimeRange(yesTimeStamp, todayTimeStamp, yesDayDatetime, todayDatetime);
        const last7DaysData = await fetchDataByTimeRange(sevenTimeStamp, null, sevenDayDatetime);
        const last30DaysData = await fetchDataByTimeRange(thirtyTimeStamp, null, thirtyDayDatetime);

        // 4. 获取全局静态数据
        const pendingOrders = await this.model('order').where({ is_delete: 0, order_status: 101 }).count();
        const totalProducts = await this.model('goods').where({ is_on_sale: 1, is_delete: 0 }).count();
        const totalUsers = await this.model('user').where({ is_delete: 0 }).count();

        // 5. 获取详细数据并格式化时间
        const newUserData = await this.model('user').where({ register_time: ['>', todayTimeStamp] }).select();
        const oldUserData = await this.model('user').where({ 
            register_time: ['<', todayTimeStamp], 
            last_login_time: ['>', todayTimeStamp] 
        }).select();
        
        // 格式化用户数据的时间字段
        if(newUserData.length > 0){
            for(const item of newUserData){
                item.register_time = moment.unix(item.register_time).format('YYYY-MM-DD HH:mm:ss');
                item.last_login_time = moment.unix(item.last_login_time).format('YYYY-MM-DD HH:mm:ss');
            }
        }
        
        if(oldUserData.length > 0){
            for(const item of oldUserData){
                item.register_time = moment.unix(item.register_time).format('YYYY-MM-DD HH:mm:ss');
                item.last_login_time = moment.unix(item.last_login_time).format('YYYY-MM-DD HH:mm:ss');
            }
        }

        // 6. 组装最终返回数据
        const info = {
            // 顶部核心指标
            total: {
                pendingOrders: pendingOrders,
                totalProducts: totalProducts,
                totalUsers: totalUsers,
            },
            // 各时间维度数据
            timeData: {
                today: todayData,
                yesterday: yesterdayData,
                last7Days: last7DaysData,
                last30Days: last30DaysData
            },
            // 其他详细数据（如果需要展示表格）
            newUserData: newUserData,
            oldUserData: oldUserData,
        };

        // 7. 返回数据
        return this.success(info);
    }
};
