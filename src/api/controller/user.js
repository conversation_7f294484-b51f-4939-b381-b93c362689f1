/*
 * @Author: huaze.fan <EMAIL>
 * @Date: 2025-03-21 15:43:48
 * @LastEditors: huaze.fan <EMAIL>
 * @LastEditTime: 2025-09-11 14:17:03
 * @FilePath: /petshop-server/src/api/controller/user.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
const Base = require('../../common/controller/base.js');
const fs = require('fs');
const _ = require('lodash');
const moment = require('moment');
module.exports = class extends Base {
    async indexAction() {
      const {user:cacheUser} = this.ctx.state
      console.log(cacheUser)
    }

    async infoAction() {
      const userId = this.post('userId');
      
      const newUserInfo = await this.model("user")
        .field("id,nickname, avatar, mobile, balance, is_agent, pet_birthday")
        .where({
          id: userId,
        })
        .find();
      newUserInfo.user_id = userId;
      newUserInfo.userType = 'user';

      // 默认地址
      const defaultAddress = await this.model('address').alias('a')
                                    .field('a.*,p.id as province_id,c.id as city_id,d.id as district_id,p.name as province_name,c.name as city_name,d.name as district_name')
                                    .join({
                                        table: 'region',
                                        join: 'left',
                                        as: 'p',
                                        on: ['p.id', 'a.province_id']
                                    })
                                    .join({
                                        table: 'region',
                                        join: 'left',
                                        as: 'c',
                                        on: ['c.id', 'a.city_id']
                                    })
                                    .join({
                                        table: 'region',
                                        join: 'left',
                                        as: 'd',
                                        on: ['d.id', 'a.district_id']
                                    })
                                    .where({
                                        user_id: userId,
                                        is_default: 1,
                                        is_delete: 0
                                    }).find();
      if (!think.isEmpty(defaultAddress)) {
          newUserInfo.defaultAddress = defaultAddress;
      }

      // 查询是否申请过代理
      const agentApply = await this.model('agent_apply').where({
          user_id: userId,
          is_delete: 0
      }).find();
      if (!think.isEmpty(agentApply)) {
          newUserInfo.review_status = agentApply.review_status;
      }

      // 查询这个手机号是否有在管理员表中
      const admin = await this.model('admin').where({
          mobile: newUserInfo.mobile,
          is_delete: 0
      }).find();
      if (!think.isEmpty(admin)) {
        newUserInfo.is_admin = 1;
      }else{
        newUserInfo.is_admin = 0;
      }

      // 将redis 中用户信息更新
      await think.cache(`user_${userId}`, newUserInfo, { timeout: 60000 * 60 });

      return this.success(newUserInfo);
    }

    /**
     * 创建充值订单
     */
    async createRechargeAction() {
      const userId = this.getLoginUserId();
      const amount = this.post('amount');
      const payType = this.post('payType'); // 'ali' 或 'wx'
      if (!amount || parseFloat(amount) <= 0) {
        return this.fail('充值金额必须大于0');
      }
      
      // 生成充值订单号
      const rechargeNo = 'RC' + moment().format('YYYYMMDDHHmmss') + _.random(100000, 999999);
      
      // 创建充值记录
      const rechargeData = {
        user_id: userId,
        recharge_no: rechargeNo,
        amount: amount,
        pay_type: payType,
        pay_status: 101, // 101：未付款
        create_time: think.datetime(new Date())
      };
      
      const rechargeId = await this.model('recharge').add(rechargeData);
      
      // 根据支付方式调用不同的支付接口
      if (payType === 'ali') {
        return this.redirect('/api/pay/aliPayRecharge?rechargeNo=' + rechargeNo + '&amount=' + amount);
      } else if (payType === 'wx') {
        return this.redirect('/api/pay/weixinPayRecharge?rechargeNo=' + rechargeNo + '&amount=' + amount);
      } else {
        return this.fail('不支持的支付方式');
      }
    }

    /**
     * 获取用户充值记录
     */
    async rechargeRecordsAction() {
      const userId = this.getLoginUserId();
      const page = this.get('page') || 1;
      const size = this.get('size') || 10;
      
      const records = await this.model('recharge').where({
        user_id: userId
      }).order('create_time DESC').page(page, size).countSelect();
      
      // 格式化时间
      for (const item of records.data) {
        if (item.pay_time) {
          item.pay_time_format = think.datetime(new Date(item.pay_time * 1000));
        }
        if (item.cancel_time) {
          item.cancel_time_format = think.datetime(new Date(item.cancel_time * 1000));
        }
        if (item.refund_time) {
          item.refund_time_format = think.datetime(new Date(item.refund_time * 1000));
        }
        
        // 添加状态文本
        switch (item.pay_status) {
          case 101:
            item.status_text = '未付款';
            break;
          case 102:
            item.status_text = '已取消';
            break;
          case 103:
            item.status_text = '已取消(系统)';
            break;
          case 201:
            item.status_text = '已付款';
            break;
          case 202:
            item.status_text = '退款中';
            break;
          case 203:
            item.status_text = '已退款';
            break;
          case 204:
            item.status_text = '拒绝退款';
            break;
          case 206:
            item.status_text = '部分退款';
            break;
          default:
            item.status_text = '未知状态';
        }
      }
      
      return this.success(records);
    }

    /**
     * 获取用户余额变动记录
     */
    async balanceLogsAction() {
      const userId = this.getLoginUserId();
      const page = this.get('page') || 1;
      const size = this.get('size') || 10;
      
      const logs = await this.model('balance_log').where({
        user_id: userId
      }).order('create_time DESC').page(page, size).countSelect();
      
      // 格式化记录
      for (const item of logs.data) {
        // 添加类型文本
        switch (item.log_type) {
          case 'RC':
            item.type_text = '充值';
            break;
          case 'CS':
            item.type_text = '消费';
            break;
          case 'RF':
            item.type_text = '退款';
            break;
          default:
            item.type_text = '其他';
        }
      }
      
      return this.success(logs);
    }

    /**
     * 获取用户余额
     */
    async balanceAction() {
      const userId = this.getLoginUserId();
      
      const user = await this.model('user').where({
        id: userId
      }).field('balance').find();
      
      return this.success({
        balance: user.balance || 0
      });
    }

    /**
     * 更新用户宠物生日
     * @returns
     */
    async updatePetBirthdayAction() {
      const userId = this.getLoginUserId();
      const petBirthday = this.post('petBirthday');
      const user = await this.model('user').where({
        id: userId
      }).update({
        pet_birthday: petBirthday
      });

      return this.success();
    }
};
