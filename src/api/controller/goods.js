const Base = require('../../common/controller/base.js');
const moment = require('moment');
const Big = require('big.js');
module.exports = class extends Base {
    async indexAction() {
        const model = this.model('goods');
        const goodsList = await model.select();
        return this.success(goodsList);
    }
    /**
     * 商品详情页数据
     * @returns {Promise.<Promise|PreventPromise|void>}
     */
    async detailAction() {
        const goodsId = this.get('id');
        const userId = this.getLoginUserId();
        const userAgent = this.getUserAgent();
        const model = this.model('goods');
        let info = await model.where({
            id: goodsId,
            is_delete: 0
        }).find();
        if (think.isEmpty(info)) {
            return this.fail('该商品不存在或已下架');
        }
        const gallery = await this.model('goods_gallery').where({
            goods_id: goodsId,
            is_delete: 0,
        }).order('sort_order').limit(6).select();
        await this.model('footprint').addFootprint(userId, goodsId);
        let productList = await model.getProductList(goodsId);
        let goodsNumber = 0;
        for (let item of productList) {
            if (item.goods_number > 0) {
                goodsNumber = goodsNumber + item.goods_number;
            }
            if (userAgent && !think.isEmpty(item.agent_price)) {
                item.real_price = item.agent_price;
            } else {
                item.real_price = item.retail_price;
            }
        }
        // 如果是代理商，则显示代理商价格
        if (userAgent && !think.isEmpty(info.min_agent_price)) {
            info.min_real_price = info.min_agent_price;
        } else {
            info.min_real_price = info.min_retail_price;
        }
        if (userAgent && !think.isEmpty(info.agent_price)) {
            info.real_price = info.agent_price;
        } else {
            info.real_price = info.retail_price;
        }
        info.goods_number = goodsNumber;
        return this.success({
            info: info,
            gallery: gallery,
            productList: productList
        });
    }
    async goodsShareAction() {
        const goodsId = this.get('id');
        const info = await this.model('goods').where({
            id: goodsId
        }).field('name,retail_price').find();
        return this.success(info);
    }
    /**
     * 获取商品列表
     * @returns {Promise.<*>}
     */
    async listAction() {
        const userId = this.getLoginUserId();
        const userAgent = this.getUserAgent();
        const keyword = this.get('keyword');
        const sort = this.get('sort');
        const order = this.get('order');
        const sales = this.get('sales');
        const model = this.model('goods');
        const whereMap = {
            is_on_sale: 1,
            is_delete: 0,
        };
        if (!think.isEmpty(keyword)) {
            whereMap.name = ['like', `%${keyword}%`];
            // 添加到搜索历史
            await this.model('search_history').add({
                keyword: keyword,
                user_id: userId,
            });
            //    TODO 之后要做个判断，这个词在搜索记录中的次数，如果大于某个值，则将他存入keyword
        }
        // 排序
        let orderMap = {};
        if (sort === 'price') {
            // 按价格
            orderMap = {
                retail_price: order
            };
        } else if (sort === 'sales') {
            // 按价格
            orderMap = {
                sell_volume: sales
            };
        } else {
            // 按商品添加时间
            orderMap = {
                sort_order: 'asc'
            };
        }
        const goodsData = await model.where(whereMap).order(orderMap).select();
        for (let goodsItem of goodsData) {
            // 如果是代理商，则显示代理商价格
            if (userAgent && !think.isEmpty(goodsItem.min_agent_price)) {
                goodsItem.min_real_price = goodsItem.min_agent_price;
            } else {
                goodsItem.min_real_price = goodsItem.min_retail_price;
            }
            if (userAgent && !think.isEmpty(goodsItem.agent_price)) {
                goodsItem.real_price = goodsItem.agent_price;
            } else {
                goodsItem.real_price = goodsItem.retail_price;
            }
        }
        return this.success(goodsData);
    }
    /**
     * 在售的商品总数
     * @returns {Promise.<Promise|PreventPromise|void>}
     */
    async countAction() {
        const goodsCount = await this.model('goods').where({
            is_delete: 0,
            is_on_sale: 1
        }).count('id');
        return this.success({
            goodsCount: goodsCount
        });
    }

    /**
     * 查询商品是否可以正常下单，并且返回当前配置的快递费用和可用的运费模板
     * addressId（默认0)
     * goodsList [{goodsId（商品id）; productId (商品类型id) number (个数)}]
     */
    async checkGoodsAction() {
        try {
            const userId = this.getLoginUserId();
            const userAgent = this.getUserAgent();
            const addressId = this.post('addressId');
            const goodsList = this.post('goodsList');
            const orderId = this.post('orderId');

            // 验证商品列表
            if (think.isEmpty(goodsList) || !Array.isArray(goodsList)) {
                return this.fail('商品列表不能为空');
            }

            // 验证商品列表格式
            for (const goods of goodsList) {
                if (!goods.goodsId || !goods.productId || !goods.number || goods.number <= 0) {
                    return this.fail('商品信息格式错误');
                }
            }

            let orderInfo = null;
            if (orderId > 0) {
                // 查出订单信息
                orderInfo = await this.model('order').where({
                    id: orderId
                }).find();
                if (think.isEmpty(orderInfo)) {
                    return this.fail('订单信息错误');
                }
            }

            const goodsModel = this.model('goods');
            const addressModel = this.model('address');

            // 检查是否存在专属运费模板的商品
            const checkPudList = await goodsModel.checkProduct(goodsList);
            if (checkPudList.length !== goodsList.length) {
                return this.fail('部分商品已下架或库存不足');
            }

            // 获取所有非包邮且有指定运费模板的商品
            const exclusiveTemplateGoods = checkPudList.filter(item =>
                item.freight_mode === 0 && item.freight_template_id > 0
            );

            let tipMsg = '';
            let filteredGoodsList = [...goodsList];

            if (exclusiveTemplateGoods.length > 0) {
                // 查询这些商品的运费模板是否为专属模板
                const templateIds = [...new Set(exclusiveTemplateGoods.map(item => item.freight_template_id))];
                const exclusiveTemplates = await this.model('freight_template').where({
                    id: ['IN', templateIds],
                    freight_use_type: 0, // 专属模板
                    is_delete: 0
                }).select();

                if (exclusiveTemplates.length > 0) {
                    // 找出使用专属模板的商品
                    const exclusiveTemplateIds = new Set(exclusiveTemplates.map(t => t.id));
                    const exclusiveGoods = exclusiveTemplateGoods
                        .filter(item => exclusiveTemplateIds.has(item.freight_template_id));

                    if (exclusiveGoods.length > 0) {
                        // 按专属模板ID分组
                        const templateGroups = {};
                        exclusiveGoods.forEach(item => {
                            const templateId = item.freight_template_id;
                            if (!templateGroups[templateId]) {
                                templateGroups[templateId] = [];
                            }
                            templateGroups[templateId].push(item);
                        });

                        const templateIds = Object.keys(templateGroups);
                        const nonExclusiveGoods = checkPudList.filter(item =>
                            !(item.freight_mode === 0 && exclusiveTemplateIds.has(item.freight_template_id))
                        );

                        // 判断处理策略
                        if (exclusiveGoods.length === checkPudList.length) {
                            // 情况1：所有商品都使用专属模板
                            if (templateIds.length > 1) {
                                // 使用了不同的专属模板，无法下单
                                return this.fail('所有商品都使用了不同的专属运费设置，无法下单');
                            }
                            // 使用相同专属模板，可以正常下单，不需要移除任何商品
                        } else {
                            // 情况2：混合订单（有专属模板商品 + 非专属模板商品）
                            if (templateIds.length > 1) {
                                // 有多个不同专属模板 + 非专属商品，选择保留策略
                                // 比较专属模板商品数量和非专属商品数量，保留数量多的
                                let keepExclusiveTemplateId = null;
                                let maxExclusiveCount = 0;

                                for (const templateId of templateIds) {
                                    if (templateGroups[templateId].length > maxExclusiveCount) {
                                        maxExclusiveCount = templateGroups[templateId].length;
                                        keepExclusiveTemplateId = templateId;
                                    }
                                }

                                if (maxExclusiveCount > nonExclusiveGoods.length) {
                                    // 保留数量最多的专属模板商品，移除其他所有商品
                                    const goodsToRemove = checkPudList.filter(item =>
                                        item.freight_template_id !== parseInt(keepExclusiveTemplateId)
                                    );

                                    const removedGoodsNames = goodsToRemove.map(item => item.goods_name);
                                    const goodsNameStr = removedGoodsNames.join('、');
                                    tipMsg = `订单商品${goodsNameStr}与专属运费商品冲突，已自动移除，请单独下单`;

                                    const removeGoodsIds = new Set(goodsToRemove.map(item => `${item.goods_id}_${item.product_id}`));
                                    filteredGoodsList = goodsList.filter(goods =>
                                        !removeGoodsIds.has(`${goods.goodsId}_${goods.productId}`)
                                    );
                                } else {
                                    // 保留非专属商品，移除所有专属模板商品
                                    const removedGoodsNames = exclusiveGoods.map(item => item.goods_name);
                                    const goodsNameStr = removedGoodsNames.join('、');
                                    tipMsg = `订单商品${goodsNameStr}存在专属运费设置，已自动移除，请单独下单`;

                                    const removeGoodsIds = new Set(exclusiveGoods.map(item => `${item.goods_id}_${item.product_id}`));
                                    filteredGoodsList = goodsList.filter(goods =>
                                        !removeGoodsIds.has(`${goods.goodsId}_${goods.productId}`)
                                    );
                                }
                            } else {
                                // 只有一个专属模板 + 非专属商品，移除专属模板商品
                                const removedGoodsNames = exclusiveGoods.map(item => item.goods_name);
                                const goodsNameStr = removedGoodsNames.join('、');
                                tipMsg = `订单商品${goodsNameStr}存在专属运费设置，已自动移除，请单独下单`;

                                const removeGoodsIds = new Set(exclusiveGoods.map(item => `${item.goods_id}_${item.product_id}`));
                                filteredGoodsList = goodsList.filter(goods =>
                                    !removeGoodsIds.has(`${goods.goodsId}_${goods.productId}`)
                                );
                            }
                        }
                    }
                }
            }

            // 新逻辑：获取所有可用的运费模板（使用过滤后的商品列表）
            const result = await this.service('goods').getAvailableFreightTemplates(
                filteredGoodsList,
                addressId,
                userId,
                userAgent,
                goodsModel,
                addressModel
            );

            // 为了保持向后兼容，同时返回默认运费（最便宜的）
            const defaultFreightPrice = result.freightTemplates.length > 0
                ? result.freightTemplates[0].freight_price
                : 0;

            return this.success({
                goodsTotalPrice: result.goodsTotalPrice,
                freightPrice: defaultFreightPrice.toFixed(2), // 默认运费（最便宜的）
                checkedAddress: result.checkedAddress,
                checkedGoodsList: result.checkedGoodsList,
                freightTemplates: result.freightTemplates, // 新增：所有可用的运费模板
                tipMsg: tipMsg // 专属模板商品提示信息
            });
        } catch (error) {
            think.logger.error('检查商品下单信息失败:', error);
            return this.fail(error.message || '检查商品信息失败');
        }
    }

    /**
     * 按运费模版归类商品并返回每个类型的商品及运费模版信息
     * addressId（默认0)
     * goodsList [{goodsId（商品id）; productId (商品类型id) number (个数)}]
     */
    async checkGoodsGroupByTemplateAction() {
        try {
            const userId = this.getLoginUserId();
            const userAgent = this.getUserAgent();
            const addressId = this.post('addressId');
            const goodsList = this.post('goodsList');
            const orderId = this.post('orderId');

            // 验证商品列表
            if (think.isEmpty(goodsList) || !Array.isArray(goodsList)) {
                return this.fail('商品列表不能为空');
            }

            // 验证商品列表格式
            for (const goods of goodsList) {
                if (!goods.goodsId || !goods.productId || !goods.number || goods.number <= 0) {
                    return this.fail('商品信息格式错误');
                }
            }

            const goodsModel = this.model('goods');
            const addressModel = this.model('address');
            let orderInfo = null;
            let tipMsg = '';

            if (orderId && orderId > 0) {
                orderInfo = await this.model('order').where({ id: orderId }).find();
            }

            // 检查商品是否存在且库存充足
            // const checkPudList = await goodsModel.checkProduct(goodsList);
            const { checkPudList, goodsTotalPrice } = await this.service('goods')._checkGoodsAndCalcTotal(goodsList, goodsModel, userAgent); 
            if (!think.isEmpty(orderInfo) && orderInfo.goods_price != goodsTotalPrice) {
                tipMsg = "商品价格发生变化";
            }
            // 检查库存校验后的商品和用户提交的商品数量是否一致，
            // 如果存在库存不足的商品，则提示用户xxx商品已下架，
            // 只保留有库存商品继续执行后续逻辑
            let filteredGoodsList = [...checkPudList];
            if (checkPudList.length !== goodsList.length) {
                const removedGoodsNames = goodsList.filter(goods => !checkPudList.find(item => item.goods_id === goods.goodsId && item.product_id === goods.productId)).map(goods => goods.goodsName);
                const goodsNameStr = removedGoodsNames.join('、');
                tipMsg = `商品${goodsNameStr}已下架，已自动移除`;
            }

            if (filteredGoodsList.length === 0) {
                return this.fail('所有商品已下架');
            }

            // 遍历 filteredGoodsList 将商品使用模版配置一直的商品归为一类
            // 目前运费模版类型分三种，
            // 一个是 freight_mode 为 1 的包邮商品，
            // 一个是 freight_mode 为 0 且 freight_template_id 为 0 的普通商品，
            // 一个是 freight_mode 为 0 且 freight_template_id 大于 0 的指定运费模板商品
            const goodsGroupByTemplate = {};
            filteredGoodsList.forEach(goods => {
                const templateId = goods.freight_mode === 1 ? 'free' : goods.freight_template_id === 0 ? 'common' : goods.freight_template_id;
                if (!goodsGroupByTemplate[templateId]) {
                    goodsGroupByTemplate[templateId] = [];
                }
                goodsGroupByTemplate[templateId].push(goods);
            });

            // 为商品添加购买数量信息
            for (const templateKey in goodsGroupByTemplate) {
                const goodsInTemplate = goodsGroupByTemplate[templateKey];
                for (const item of goodsInTemplate) {
                    const match = goodsList.find(g => g.goodsId == item.goods_id && g.productId == item.product_id);
                    if (match) {
                        item.buy_number = match.number;
                        // 如果是代理商，使用代理价格
                        if (userAgent && item.agent_price) {
                            item.retail_price = item.agent_price;
                        }
                    }
                }
            }

            // 获取收货地址
            let address = null;
            if (addressId) {
                address = await addressModel.where({ id: addressId, user_id: userId, is_delete: 0 }).find();
            } else {
                address = await addressModel.where({ user_id: userId, is_default: 1, is_delete: 0 }).find();
            }

            if (think.isEmpty(address)) {
                return this.fail(addressId ? '收货地址有误' : '请选择收货地址');
            }

            // 调用 service 层方法进行运费计算
            const result = await this.service('goods').goodsGroupByFreightTemplates(
                goodsGroupByTemplate,
                addressId,
                userId,
                userAgent,
                goodsModel,
                addressModel
            );

            // 如果是从待支付过来的订单，检查当前订单是否修改了价格，如果修改了价格则返回给前端
            let discountFlag = "N";
            let goodsDiscountPrice = null;
            if (!think.isEmpty(orderInfo) && orderInfo.goods_discount_price != 0) {
                // 有改价优惠
                discountFlag = "Y";
                goodsDiscountPrice = orderInfo.goods_discount_price;
            }

            return this.success({
                address: result.address,
                template_groups: result.template_groups,
                discountFlag: discountFlag,
                goodsDiscountPrice: goodsDiscountPrice,
                tip_msg: tipMsg
            });

        } catch (error) {
            think.logger.error('按运费模版归类商品失败:', error);
            return this.fail(error.message || '按运费模版归类商品失败');
        }
    }
};