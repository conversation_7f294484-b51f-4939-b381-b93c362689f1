/*
 * @Author: <PERSON><PERSON> <EMAIL>
 * @Date: 2025-01-01 00:00:00
 * @LastEditors: <PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2025-09-05 23:29:27
 * @FilePath: /petshop-server/src/api/service/coupon.js
 * @Description: 优惠券服务类
 */
const moment = require('moment');

module.exports = class extends think.Service {
  /**
   * 验证优惠券是否可用
   */
  async validateCoupon(couponCode, userId, orderAmount) {
    if (!couponCode) {
      return { valid: false, message: '优惠券码不能为空' };
    }

    // 查找优惠券
    const coupon = await this.model('user_coupon').alias('uc')
      .field('uc.*, ct.name as template_name, ct.type, ct.discount, ct.min_amount')
      .join({
        table: 'coupon_template',
        join: 'left',
        as: 'ct',
        on: ['ct.id', 'uc.coupon_template_id']
      })
      .where({
        'uc.coupon_code': couponCode,
        'uc.user_id': userId,
        'uc.is_used': 0,
        'uc.is_delete': 0
      })
      .find();

    if (think.isEmpty(coupon)) {
      return { valid: false, message: '优惠券不存在或已使用' };
    }

    // 检查订单数据中是否存在已支付并使用了这个优惠券的订单
    // 101：未付款、102：已取消、103已取消(系统)、201：已付款、202：申请订单取消，退款中、203：已退款、204：拒绝退款、205：同意退款，等待退货，301：已发货、302：已收货、303：已收货(系统)、401：已完成、801：拼团中,未付款、802：拼团中,已付款
    const existingOrder = await this.model('order').where({
      user_id: userId,
      coupon_code: couponCode,
      // order_status 大于 200
      order_status: ['>', 200],
      is_delete: 0
    }).find();

    if (!think.isEmpty(existingOrder)) {
      return { valid: false, message: '优惠券已使用' };
    }

    // 检查有效期
    const now = moment();
    const validStart = moment(coupon.valid_start);
    const validEnd = moment(coupon.valid_end);

    if (now.isBefore(validStart)) {
      return { valid: false, message: '优惠券还未生效' };
    }

    if (now.isAfter(validEnd)) {
      return { valid: false, message: '优惠券已过期' };
    }

    // 检查使用门槛
    if (coupon.min_amount > 0 && orderAmount < coupon.min_amount) {
      return { 
        valid: false, 
        message: `订单金额需满${coupon.min_amount}元才能使用此优惠券` 
      };
    }

    return {
      valid: true,
      coupon: coupon,
      discountAmount: this.calculateDiscount(coupon, orderAmount)
    };
  }

  /**
   * 计算优惠券折扣金额
   */
  calculateDiscount(coupon, orderAmount) {
    switch (coupon.type) {
      case 1: // 满减券
        return Math.min(coupon.discount, orderAmount);
      case 2: // 无门槛券
        return Math.min(coupon.discount, orderAmount);
      case 3: // 折扣券
        const discountRate = coupon.discount / 100; // 折扣率，如 90 表示 9 折
        return parseFloat(orderAmount * (1 - discountRate)).toFixed(2);
      default:
        return 0;
    }
  }

  /**
   * 使用优惠券
   */
  async useCoupon(couponCode, userId, orderId) {
    try {
      // 更新优惠券状态为已使用
      const updateResult = await this.model('user_coupon')
        .where({
          coupon_code: couponCode,
          user_id: userId,
          is_used: 0,
          is_delete: 0
        })
        .update({
          is_used: 1,
          order_id: orderId,
        });

      if (updateResult === 0) {
        throw new Error('优惠券状态更新失败');
      }

      return true;
    } catch (error) {
      think.logger.error('使用优惠券失败:', error.message);
      return false;
    }
  }

  /**
   * 退还优惠券（订单取消时）
   */
  async refundCoupon(couponCode, userId) {
    try {
      // 将优惠券状态改回未使用
      const updateResult = await this.model('user_coupon')
        .where({
          coupon_code: couponCode,
          user_id: userId,
          is_used: 1,
          is_delete: 0
        })
        .update({
          is_used: 0
        });

      if (updateResult === 0) {
        throw new Error('优惠券退还失败');
      }

      return true;
    } catch (error) {
      think.logger.error('退还优惠券失败:', error.message);
      return false;
    }
  }

  /**
   * 获取用户可用的优惠券列表（用于订单页面选择）
   */
  async getAvailableCouponsForOrder(userId, orderAmount) {
    const now = moment().format('YYYY-MM-DD HH:mm:ss');
    
    const coupons = await this.model('user_coupon').alias('uc')
      .field('uc.*, ct.name as template_name, ct.type, ct.discount, ct.min_amount')
      .join({
        table: 'coupon_template',
        join: 'left',
        as: 'ct',
        on: ['ct.id', 'uc.coupon_template_id']
      })
      .where({
        'uc.user_id': userId,
        'uc.is_used': 0,
        'uc.is_delete': 0,
        'uc.valid_start': ['<=', now],
        'uc.valid_end': ['>=', now]
      })
      .order('ct.min_amount ASC, uc.create_time DESC')
      .select();

    // 计算每张优惠券的可用性和折扣金额
    const availableCoupons = coupons.map(coupon => {
      const canUse = orderAmount >= coupon.min_amount;
      const discountAmount = canUse ? this.calculateDiscount(coupon, orderAmount) : 0;
      
      return {
        ...coupon,
        can_use: canUse,
        discount_amount: discountAmount,
        final_amount: parseFloat(orderAmount - discountAmount).toFixed(2)
      };
    });

    return availableCoupons;
  }
}; 