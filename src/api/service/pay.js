/*
 * @Author: huaze.fan <EMAIL>
 * @Date: 2025-04-18 16:28:17
 * @LastEditors: <PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2025-09-11 01:02:15
 * @FilePath: /petshop-server/src/api/service/pay.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
// src/service/pay.js
const Base = require('../../common/service/base.js');
const Big = require('big.js');
module.exports = class extends Base {

    /**
     * 处理退款订单状态更新的通用方法
     * @param {Object} orderInfo - 订单信息
     * @param {number} refundAmount - 退款金额
     * @param {string} refundReason - 退款原因
     * @param {Object} session - 数据库事务会话（可选）
     * @returns {Promise<Object>} 返回更新后的订单状态信息
     */
    async processRefundOrderUpdate(orderInfo, refundAmount, refundReason = '', session = null) {
        const dbModel = session ? this.model('order').db(session) : this.model('order');

        // 计算退款金额和更新已退款总额
        const refundAmountBig = new Big(refundAmount);
        const currentRefundedPrice = new Big(orderInfo.refunded_price || 0);
        const newRefundedPrice = currentRefundedPrice.plus(refundAmountBig);

        // 判断是否为全额退款 - 考虑组合支付场景
        let isFullRefund;

        if (orderInfo.pay_type && orderInfo.pay_type.includes('+bal')) {
            // 组合支付：判断是否退完了第三方支付部分
            const primaryPayAmount = new Big(orderInfo.primary_pay_amount || 0);
            isFullRefund = refundAmountBig.gte(primaryPayAmount);
        } else {
            // 非组合支付：按原逻辑判断
            const actualPrice = new Big(orderInfo.actual_price);
            isFullRefund = newRefundedPrice.gte(actualPrice);
        }

        // 判断是否为组合支付余额不足的自动退款
        const isBalanceInsufficientRefund = refundReason.includes('余额不足');

        // 设置订单退款状态
        let updateInfo = {
            order_status: (isFullRefund ? 203 : 206), // 全额退款或部分退款
            refund_time: parseInt(new Date().getTime() / 1000),
            refunded_price: newRefundedPrice.toFixed(2)
        };

        // 设置退款原因
        if (refundReason) {
            updateInfo.refund_reason = refundReason;
        }

        // 更新订单状态
        await dbModel.where({
            id: orderInfo.id
        }).update(updateInfo);

        think.logger.info(`订单 ${orderInfo.order_sn} 退款状态更新成功，退款金额：${newRefundedPrice.toFixed(2)}，新状态：${updateInfo.order_status}`);

        return {
            isFullRefund,
            newRefundedPrice,
            orderStatus: updateInfo.order_status,
            isBalanceInsufficientRefund
        };
    }
};
