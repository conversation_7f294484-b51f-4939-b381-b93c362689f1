/*
 * @Author: huaze.fan <EMAIL>
 * @Date: 2025-03-21 15:43:48
 * @LastEditors: <PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2025-09-11 00:48:30
 * @FilePath: /petshop-server/src/api/model/goods.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
module.exports = class extends think.Model {
    getTableName() {
      return this.tablePrefix + 'goods';
    }
    /**
     * 获取商品的product
     * @param goodsId
     * @returns {Promise.<*>}
     */
    async getProductList(goodsId) {
        const goods = await this.model('product').where({goods_id: goodsId,is_delete:0}).select();
        return goods;
    }

    /**
     * 校验商品和规格是否可以正常下单，并返回规格信息
     * @param goodsList
     * @returns {Promise.<*>}
     */
    async checkProduct(goodsList) {
      const checkList = await Promise.all(goodsList.map(item => {
        return this.model('product').alias('pd')
          .join({
            table: 'goods',
            join: 'left',
            as: 'gd',
            on: ['pd.goods_id', 'gd.id']
          })
          .field([
            'gd.freight_template_id as freight_template_id',
            'gd.freight_mode as freight_mode',
            'gd.name as goods_name',
            'gd.list_pic_url as list_pic_url',
            'pd.id as product_id',
            'pd.goods_sn as goods_sn',
            'pd.goods_weight as goods_weight',
            'pd.retail_price as retail_price',
            'pd.goods_number as goods_number',
            'pd.market_price as market_price',
            'pd.agent_price as agent_price',
            'pd.goods_name as product_name',
            'pd.goods_id as goods_id',
            'pd.goods_specifition_name as  goods_specifition_name'
          ])
          .where({
            'gd.id': item.goodsId,
            'pd.id': item.productId,
            'gd.is_on_sale': 1,
            'gd.is_delete': 0,
            'pd.is_on_sale': 1,
            'pd.is_delete': 0,
            'pd.goods_number': ['>=', item.number]
          })
          .find();
      }));
      return checkList.filter(item => item.product_id != null);;
    }
};
