/*
 * @Author: huaze.fan <EMAIL>
 * @Date: 2025-03-21 15:43:48
 * @LastEditors: <PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2025-09-11 00:48:25
 * @FilePath: /petshop-server/src/api/model/goods.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
module.exports = class extends think.Model {
    getTableName() {
      return this.tablePrefix + 'product';
    }
};
